.tree-diagram {
  width: 100%;
  padding: 20px;
  overflow-x: auto;
  overflow-y: hidden;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.tree-no-data {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-size: 16px;
}

.tree-root {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  min-width: max-content;
}

.tree-node {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin: 0;
  position: relative;
}

.tree-node__content {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  position: relative;
}

.tree-node__label {
  background: #fff;
  border: 2px solid #007bff;
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.tree-node__label:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tree-node__label--root {
  background: #007bff;
  color: white;
  font-weight: 600;
  font-size: 16px;
  border-color: #0056b3;
}

.tree-node__label--root:hover {
  background: #0056b3;
}

.tree-node__connector {
  width: 30px;
  height: 2px;
  background: #007bff;
  margin-left: 10px;
  position: relative;
}

.tree-node__connector::after {
  content: '';
  position: absolute;
  right: -2px;
  top: -3px;
  width: 0;
  height: 0;
  border-left: 6px solid #007bff;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.tree-node__children {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-left: 50px;
  position: relative;
}

.tree-node__children::before {
  content: '';
  position: absolute;
  left: -25px;
  top: -10px;
  bottom: 50%;
  width: 2px;
  background: #007bff;
}

.tree-node__children .tree-node {
  position: relative;
}

.tree-node__children .tree-node::before {
  content: '';
  position: absolute;
  left: -25px;
  top: 15px;
  width: 15px;
  height: 2px;
  background: #007bff;
}

/* 不同层级的样式 */
.tree-node--level-1 .tree-node__label {
  background: #28a745;
  border-color: #1e7e34;
  color: white;
}

.tree-node--level-1 .tree-node__label:hover {
  background: #1e7e34;
}

.tree-node--level-1 .tree-node__connector {
  background: #28a745;
}

.tree-node--level-1 .tree-node__connector::after {
  border-left-color: #28a745;
}

.tree-node--level-2 .tree-node__label {
  background: #ffc107;
  border-color: #e0a800;
  color: #333;
}

.tree-node--level-2 .tree-node__label:hover {
  background: #e0a800;
  color: white;
}

.tree-node--level-2 .tree-node__connector {
  background: #ffc107;
}

.tree-node--level-2 .tree-node__connector::after {
  border-left-color: #ffc107;
}

.tree-node--level-3 .tree-node__label {
  background: #dc3545;
  border-color: #c82333;
  color: white;
}

.tree-node--level-3 .tree-node__label:hover {
  background: #c82333;
}

.tree-node--level-3 .tree-node__connector {
  background: #dc3545;
}

.tree-node--level-3 .tree-node__connector::after {
  border-left-color: #dc3545;
}

.tree-node--level-4 .tree-node__label {
  background: #6f42c1;
  border-color: #5a32a3;
  color: white;
}

.tree-node--level-4 .tree-node__label:hover {
  background: #5a32a3;
}

.tree-node--level-4 .tree-node__connector {
  background: #6f42c1;
}

.tree-node--level-4 .tree-node__connector::after {
  border-left-color: #6f42c1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tree-diagram {
    padding: 10px;
  }
  
  .tree-node__label {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .tree-node__label--root {
    font-size: 14px;
  }
  
  .tree-node__connector {
    width: 20px;
  }
  
  .tree-node__children {
    margin-left: 35px;
    gap: 10px;
  }
  
  .tree-node__children::before {
    left: -18px;
  }
  
  .tree-node__children .tree-node::before {
    left: -18px;
    width: 10px;
  }
}
