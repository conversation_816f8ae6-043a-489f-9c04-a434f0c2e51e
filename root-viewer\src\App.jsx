import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Layout from './components/layout/Layout'
import EventPage from './pages/EventPage'
import ChannelPage from './pages/ChannelPage'
import './App.css'

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          {/* 事件页面路由 */}
          <Route path="/:category/:eventId" element={<EventPage />} />
          {/* 通道页面路由 */}
          <Route path="/:category/:eventId/:channelId" element={<ChannelPage />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default App
